<?php

namespace App\Repositories;

use App\Models\API\Xaydung\Congtrinh_xd;
use App\Repositories\Contracts\Congtrinh_xdRepositoryInterface;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

class Congtrinh_xdRepository implements Congtrinh_xdRepositoryInterface
{
    protected $model;
    protected $cachePrefix = 'congtrinh_xd_';
    protected $cacheTTL = 3600; // 1 hour
    protected $cacheTag = 'congtrinh_xd-list'; // Cache tag cho việc xóa cache có chủ đích

    public function __construct(Congtrinh_xd $model)
    {
        $this->model = $model;
    }

    public function paginate(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        // Lấy số trang hiện tại từ request
        $currentPage = request('page', 1);
        $cacheKey = $this->cachePrefix . 'list_' . md5(json_encode($filters) . $perPage . $currentPage);

        // Sử dụng Cache Tags nếu driver hỗ trợ, nếu không thì dùng cache thông thường
        return $this->cacheWithTags($cacheKey, function () use ($filters, $perPage) {
            // <<< THAY ĐỔI: Sử dụng Eager Loading >>>
            $query = $this->model->with(['xa'/* , 'quyettoan' */]) // Tải relationship
                // <<< THAY ĐỔI: Select cột bảng chính + geom >>>
                ->selectRaw('xaydung.congtrinh_xd.*, ST_AsGeoJSON(xaydung.congtrinh_xd.geom) as geom');

            // <<< THAY ĐỔI: Bỏ alias 'cd' khi filter >>>
            if (isset($filters['id_xa'])) {
                $query->where('id_xa', $filters['id_xa']);
            }
            if (isset($filters['stt_thua'])) {
                $query->where('stt_thua', $filters['stt_thua']);
            }
            if (isset($filters['sh_tobando'])) {
                $query->where('sh_tobando', $filters['sh_tobando']);
            }

            if (isset($filters['search'])) {
                $searchTerm = trim($filters['search']);
                $query->where(function($q) use ($searchTerm) {
                    // <<< THAY ĐỔI: Bỏ alias 'cd.' >>>
                    $q->where('nam_capnhat', 'ilike', '%'.$searchTerm.'%')
                      // <<< THAY ĐỔI: Tìm trên relationship bằng orWhereHas >>>
                      ->orWhereHas('xa', function ($subQuery) use ($searchTerm) {
                          // <<< Đảm bảo cột 'tenxa' tồn tại trong bảng rg_xa >>>
                          $subQuery->where('tenxa', 'ilike', '%'.$searchTerm.'%');
                      });
                      /* ->orWhereHas('quyettoan', function ($subQuery) use ($searchTerm) {
                          // <<< Đảm bảo cột 'nguyengia' tồn tại trong bảng quyettoan >>>
                          // Cần cast sang text nếu nguyengia là kiểu số
                          $subQuery->whereRaw('CAST(nguyengia AS TEXT) ilike ?', ['%'.$searchTerm.'%']);
                      }); */
                });
            }

            return $query->paginate($perPage);
        });
    }

    /**
     * Phân trang chỉ lấy geometry và ID
     * Tối ưu tài nguyên khi chỉ cần hiển thị dữ liệu không gian
     */
    public function paginateGeometryOnly(array $filters = []): LengthAwarePaginator
    {
        $cacheKey = $this->cachePrefix . 'geometry_list_' . md5(json_encode($filters));

        return $this->cacheWithTags($cacheKey, function () use ($filters) {
            $query = $this->model
                ->selectRaw('xaydung.congtrinh_xd.id, ST_AsGeoJSON(xaydung.congtrinh_xd.geom) as geom');

            // Áp dụng các bộ lọc
            if (isset($filters['id_xa'])) {
                $query->where('id_xa', $filters['id_xa']);
            }
            if (isset($filters['stt_thua'])) {
                $query->where('stt_thua', $filters['stt_thua']);
            }
            if (isset($filters['sh_tobando'])) {
                $query->where('sh_tobando', $filters['sh_tobando']);
            }

            if (isset($filters['search'])) {
                $searchTerm = trim($filters['search']);
                $query->where(function($q) use ($searchTerm) {
                    $q->where('nam_capnhat', 'ilike', '%'.$searchTerm.'%')
                      ->orWhereHas('xa', function ($subQuery) use ($searchTerm) {
                          $subQuery->where('tenxa', 'ilike', '%'.$searchTerm.'%');
                      });
                     /*  ->orWhereHas('quyettoan', function ($subQuery) use ($searchTerm) {
                          $subQuery->whereRaw('CAST(nguyengia AS TEXT) ilike ?', ['%'.$searchTerm.'%']);
                      }); */
                });
            }

            // Không giới hạn số lượng bản ghi để lấy tất cả geometry
            return $query->paginate(1000);
        });
    }

    /**
     * Phân trang chỉ lấy thuộc tính (không lấy geometry)
     * Tối ưu tài nguyên khi chỉ cần hiển thị thông tin thuộc tính
     */
    public function paginateAttributesOnly(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        // Lấy số trang hiện tại từ request
        $currentPage = request('page', 1);
        $cacheKey = $this->cachePrefix . 'attributes_list_' . md5(json_encode($filters) . $perPage . $currentPage);

        return $this->cacheWithTags($cacheKey, function () use ($filters, $perPage) {
            $query = $this->model->with(['xa'/* , 'quyettoan' */])
                ->selectRaw('
                    xaydung.congtrinh_xd.id,
                    xaydung.congtrinh_xd.stt_thua,
                    xaydung.congtrinh_xd.id_xa,
                    xaydung.congtrinh_xd.sh_tobando,
                    xaydung.congtrinh_xd.nam_capnhat,
                    xaydung.congtrinh_xd.dientich_m2,
                ');

            // Áp dụng các bộ lọc
            if (isset($filters['id_xa'])) {
                $query->where('id_xa', $filters['id_xa']);
            }
            if (isset($filters['stt_thua'])) {
                $query->where('stt_thua', $filters['stt_thua']);
            }
            if (isset($filters['sh_tobando'])) {
                $query->where('sh_tobando', $filters['sh_tobando']);
            }

            if (isset($filters['search'])) {
                $searchTerm = trim($filters['search']);
                $query->where(function($q) use ($searchTerm) {
                    $q->where('nam_capnhat', 'ilike', '%'.$searchTerm.'%')
                      ->orWhereHas('xa', function ($subQuery) use ($searchTerm) {
                          $subQuery->where('tenxa', 'ilike', '%'.$searchTerm.'%');
                      });
                      /* ->orWhereHas('quyettoan', function ($subQuery) use ($searchTerm) {
                          $subQuery->whereRaw('CAST(nguyengia AS TEXT) ilike ?', ['%'.$searchTerm.'%']);
                      }); */
                });
            }

            return $query->paginate($perPage);
        });
    }

    public function findById(string $id): ?Congtrinh_xd
    {
        $cacheKey = $this->cachePrefix . $id;

        return $this->cacheWithTags($cacheKey, function () use ($id) {
            // <<< THAY ĐỔI: Sử dụng Eager Loading và find() >>>
             return $this->model->with(['xa'/* , 'quyettoan' */]) // Tải relationship
                // <<< THAY ĐỔI: Select cột bảng chính + geom >>>
                ->selectRaw('xaydung.congtrinh_xd.*, ST_AsGeoJSON(xaydung.congtrinh_xd.geom) as geom')
                ->find($id); // Sử dụng find()
        });
    }

    public function create(array $payload): Congtrinh_xd // Đổi tên $data thành $payload để rõ ràng hơn
    {
        DB::beginTransaction();
        try {
            $attributes = [];
            if (isset($payload['properties']) && is_array($payload['properties'])) {
                $attributes = $payload['properties'];
            }

            if (isset($payload['geometry'])) {
                $attributes['geom'] = Congtrinh_xd::geometryFromGeoJSON(json_encode($payload['geometry']));
            }
            // $payload['id'] là ID của feature GeoJSON, không phải ID chính của model Congtrinh_xd.
            // Model Congtrinh_xd tự tạo ID chính (ví dụ: UUID) thông qua model event.

            $created = $this->model->create($attributes);

            // <<< THAY ĐỔI: Tải lại model với selectRaw cho geom >>>
            $reloadedCongtrinh_xd = $this->model
                ->selectRaw('xaydung.congtrinh_xd.*, ST_AsGeoJSON(xaydung.congtrinh_xd.geom) as geom')
                ->find($created->id);

            DB::commit();
            $this->clearListCache();

            // Trả về model vừa tạo (Resource sẽ xử lý việc load relationship nếu cần)
            return $reloadedCongtrinh_xd;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function bulkCreate(array $dataArray): array
    {
        $results = [];
        $errors = [];

        DB::beginTransaction();
        try {
            foreach ($dataArray as $index => $payload) {
                try {
                    $attributes = [];
                    if (isset($payload['properties']) && is_array($payload['properties'])) {
                        $attributes = $payload['properties'];
                    } else {
                        // If payload is already flattened (from import service)
                        $attributes = $payload;
                    }

                    if (isset($payload['geometry'])) {
                        $attributes['geom'] = Congtrinh_xd::geometryFromGeoJSON(json_encode($payload['geometry']));
                    }

                    $created = $this->model->create($attributes);

                    // Reload model with geometry for consistent response
                    $reloadedCongtrinh_xd = $this->model
                        ->selectRaw('xaydung.congtrinh_xd.*, ST_AsGeoJSON(xaydung.congtrinh_xd.geom) as geom')
                        ->find($created->id);

                    $results[] = $reloadedCongtrinh_xd;

                } catch (\Exception $e) {
                    $errors[] = [
                        'index' => $index,
                        'error' => $e->getMessage(),
                        'data' => $payload
                    ];

                    // Continue processing other records
                    continue;
                }
            }

            DB::commit();
            $this->clearListCache();

            return [
                'successful' => $results,
                'errors' => $errors,
                'total_processed' => count($dataArray),
                'successful_count' => count($results),
                'error_count' => count($errors)
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function update(string $id, array $payload): ?Congtrinh_xd // Đổi tên $data thành $payload
    {
        $this->clearCache($id);

        DB::beginTransaction();
        try {
            $congtrinh_xd = $this->model->find($id);

            if (!$congtrinh_xd) {
                DB::rollBack();
                return null;
            }

            $attributesToUpdate = [];
            if (isset($payload['properties']) && is_array($payload['properties'])) {
                $attributesToUpdate = $payload['properties'];
            }

            if (isset($payload['geometry'])) {
                $attributesToUpdate['geom'] = Congtrinh_xd::geometryFromGeoJSON(json_encode($payload['geometry']));
            }

            // ID chính của model ($id) không nên được cập nhật từ $payload['properties']['id'] (nếu có)
            // hoặc $payload['id'] (feature ID).
            // Đảm bảo rằng $attributesToUpdate không chứa ID chính nếu nó không được phép thay đổi.

            if (!empty($attributesToUpdate)) {
                $congtrinh_xd->update($attributesToUpdate);
            }

            // <<< THAY ĐỔI: Tải lại model với selectRaw cho geom >>>
            $reloadedCongtrinh_xd = $this->model
                ->selectRaw('xaydung.congtrinh_xd.*, ST_AsGeoJSON(xaydung.congtrinh_xd.geom) as geom')
                ->find($congtrinh_xd->id); // Sử dụng $congtrinh_xd->id thay vì $id để chắc chắn

            DB::commit();
            $this->clearListCache();

            // Trả về model vừa cập nhật
            return $reloadedCongtrinh_xd;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }


    public function delete(string $id): bool
    {
        DB::beginTransaction();
        try {
            $deleted = $this->model->where('id', $id)->delete();

            DB::commit();

            if ($deleted) {
                $this->clearCache($id);
                $this->clearListCache();
            }

            return $deleted > 0;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function checkExists(string $id): bool
    {
        return $this->model->where('id', $id)->exists();
    }

    protected function clearCache(string $id): void
    {
        Cache::forget($this->cachePrefix . $id);
    }

    protected function clearListCache(): void
    {
        // Sử dụng Cache Tags để xóa tất cả cache liên quan đến danh sách congtrinh_xd
        // Nếu driver không hỗ trợ tags, xóa cache theo cách cũ
        if ($this->supportsTags()) {
            Cache::tags([$this->cacheTag])->flush();
        } else {
            // Fallback: xóa cache theo pattern (chỉ hoạt động với một số driver)
            $this->clearCacheByPattern();
        }
    }

    /**
     * Kiểm tra xem cache driver có hỗ trợ tags không
     */
    protected function supportsTags(): bool
    {
        $driver = config('cache.default');
        $supportedDrivers = ['redis', 'memcached', 'array'];
        return in_array($driver, $supportedDrivers);
    }

    /**
     * Cache với tags nếu hỗ trợ, nếu không thì dùng cache thông thường với version
     */
    protected function cacheWithTags(string $key, callable $callback)
    {
        if ($this->supportsTags()) {
            return Cache::tags([$this->cacheTag])->remember($key, $this->cacheTTL, $callback);
        } else {
            // Thêm version vào cache key để có thể invalidate
            $versionKey = $this->cachePrefix . 'version';
            $version = Cache::get($versionKey, 0);
            $versionedKey = $key . '_v' . $version;
            return Cache::remember($versionedKey, $this->cacheTTL, $callback);
        }
    }

    /**
     * Xóa cache theo pattern (fallback cho drivers không hỗ trợ tags)
     */
    protected function clearCacheByPattern(): void
    {
        // Với database driver, chúng ta không thể xóa theo pattern
        // Thay vào đó, chúng ta sẽ tăng cache version để invalidate tất cả cache
        $versionKey = $this->cachePrefix . 'version';
        $currentVersion = Cache::get($versionKey, 0);
        Cache::put($versionKey, $currentVersion + 1, $this->cacheTTL * 2);
    }

     // Hàm rememberWithKeyTracking (nếu có) không cần thay đổi
     protected function rememberWithKeyTracking($key, $ttl, $callback, $listKeyPrefix = null)
     {
         $result = Cache::remember($key, $ttl, $callback);
         if ($listKeyPrefix) {
             $listKey = '__keys__' . $listKeyPrefix;
             $keys = Cache::get($listKey, []);
             if (!in_array($key, $keys)) {
                 $keys[] = $key;
                 Cache::put($listKey, $keys, $ttl + 60); // Lưu danh sách key lâu hơn một chút
             }
         }
         return $result;
     }
}
