<?php

namespace App\Models\API\Basemap;

use App\Models\API\Taisan\Congdap;
use Illuminate\Database\Eloquent\Model;

class Rgxa extends Model
{
    protected $connection = 'pgsql'; // <<< Hoặc connection mặc định của bạn
    protected $table = 'datcong.ranhgioi'; // <<< Tên bảng chính xác
    protected $primaryKey = 'id';
    public $incrementing = false;
    public $timestamps = false;
    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'geom',
        'ten',
        'tinh',
        'id_tinh',
        'dientich',
        'danso'
    ];

    public function congdaps()
    {
        return $this->hasMany(Congdap::class, 'id_xa', 'id');
    }
}
