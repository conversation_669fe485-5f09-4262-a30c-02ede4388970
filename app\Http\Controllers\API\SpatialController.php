<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\API\Basemap\Rgxa;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class SpatialController extends Controller
{
    use ApiResponseTrait;

    /**
     * Tìm xã dựa trên tọa độ (point-in-polygon query)
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function findXaByCoordinates(Request $request)
    {
        try {
            // Validate input
            $validator = Validator::make($request->all(), [
                'longitude' => 'required|numeric|between:-180,180',
                'latitude' => 'required|numeric|between:-90,90'
            ]);

            if ($validator->fails()) {
                return $this->errorResponse(
                    'Dữ liệu tọa độ không hợp lệ: ' . $validator->errors()->first(),
                    400
                );
            }

            $longitude = $request->input('longitude');
            $latitude = $request->input('latitude');

            // Thực hiện spatial query để tìm xã chứa điểm
            $xa = Rgxa::select('id', 'ten', 'id_tinh', 'tinh')
                ->whereRaw('ST_Contains(geom, ST_SetSRID(ST_MakePoint(?, ?), 4326))', [$longitude, $latitude])
                ->first();

            if (!$xa) {
                return $this->successResponse(
                    data: null,
                    message: 'Không tìm thấy xã tại tọa độ này'
                );
            }

            return $this->successResponse(
                data: [
                    'id_xa' => $xa->id,
                    'ten_xa' => $xa->ten,
                    'id_tinh' => $xa->id_tinh,
                    'ten_tinh' => $xa->tinh,
                    'coordinates' => [
                        'longitude' => $longitude,
                        'latitude' => $latitude
                    ]
                ],
                message: 'Tìm thấy thông tin xã thành công'
            );

        } catch (\Exception $e) {
            Log::error('Error in spatial query: ' . $e->getMessage(), [
                'longitude' => $request->input('longitude'),
                'latitude' => $request->input('latitude'),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->errorResponse(
                'Lỗi khi thực hiện truy vấn không gian: ' . $e->getMessage(),
                500
            );
        }
    }

    /**
     * Lấy danh sách các xã (administrative areas)
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getXaList(Request $request)
    {
        try {
            // Validate input parameters
            $validator = Validator::make($request->all(), [
                'search' => 'nullable|string|max:255',
                'id_huyen' => 'nullable|string',
                'per_page' => 'nullable|integer|min:1|max:100'
            ]);

            if ($validator->fails()) {
                return $this->validationErrorResponse($validator->errors());
            }

            // Get pagination and filter parameters
            $perPage = $request->input('per_page', 15);
            $search = $request->input('search');
            $idHuyen = $request->input('id_huyen');

            // Build query
            $query = Rgxa::select([
                'id',
                'ten',
                'id_tinh',
                'tinh',
                'dientich',
                'danso'
            ]);

            // Apply filters
            if ($search) {
                $searchTerm = trim($search);
                $query->where(function($q) use ($searchTerm) {
                    $q->where('ten', 'ilike', '%' . $searchTerm . '%')
                      ->orWhere('tinh', 'ilike', '%' . $searchTerm . '%');
                });
            }

            if ($idHuyen) {
                $query->where('id_tinh', $idHuyen);
            }

            // Order by xa name for consistent results
            $query->orderBy('ten', 'asc');

            // Paginate results
            $paginator = $query->paginate($perPage);

            // Transform data for API response
            $transformedData = $paginator->getCollection()->map(function ($xa) {
                return [
                    'id' => $xa->id,
                    'ten_xa' => $xa->ten,
                    'id_tinh' => $xa->id_tinh,
                    'ten_tinh' => $xa->tinh,
                    'dien_tich' => $xa->dientich,
                    'dan_so' => $xa->danso
                ];
            });

            // Create response with pagination metadata
            return response()->json([
                'success' => true,
                'message' => 'Lấy danh sách xã thành công',
                'data' => $transformedData,
                'meta' => [
                    'current_page' => $paginator->currentPage(),
                    'per_page' => $paginator->perPage(),
                    'total' => $paginator->total(),
                    'last_page' => $paginator->lastPage(),
                    'from' => $paginator->firstItem(),
                    'to' => $paginator->lastItem(),
                ],
                'links' => [
                    'first' => $paginator->url(1),
                    'last' => $paginator->url($paginator->lastPage()),
                    'prev' => $paginator->previousPageUrl(),
                    'next' => $paginator->nextPageUrl(),
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Error fetching xa list: ' . $e->getMessage(), [
                'search' => $request->input('search'),
                'id_huyen' => $request->input('id_huyen'),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->errorResponse(
                'Lỗi khi lấy danh sách xã: ' . $e->getMessage(),
                500
            );
        }
    }
}
